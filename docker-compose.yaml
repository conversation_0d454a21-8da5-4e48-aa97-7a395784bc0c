version: '3.8'

services:
  open-webui-1:
    image: ghcr.io/open-webui/open-webui:main
    container_name: open-webui-1
    # volumes:
    #   - shared-data:/app/backend/data/
    ports:
      - "3000:8080"  # Change if needed
    restart: unless-stopped
    networks:
      - isolated_network

  open-webui-2:
    image: ghcr.io/open-webui/open-webui:main
    container_name: open-webui-2
    # volumes:
    #   - shared-data:/app/backend/data/
    ports:
      - "3001:8080"  # Change if needed
    restart: unless-stopped
    networks:
      - isolated_network

volumes:
  shared-data:

networks:
  isolated_network:
    driver: bridge
    internal: true
